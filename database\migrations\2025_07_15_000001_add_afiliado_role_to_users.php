<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Modificar o enum para incluir 'afiliado'
            $table->enum('role', ['admin', 'fisioterapeuta', 'paciente', 'afiliado'])->default('paciente')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Voltar ao enum original
            $table->enum('role', ['admin', 'fisioterapeuta', 'paciente'])->default('paciente')->change();
        });
    }
};
